import { useState } from 'react'
import { IconButton } from '../components/icon-button/icon-button'
import { PrimaryButton } from '../components/primary-button/primary-button'
import { SecondaryButton } from '../components/secondary-button/secondary-button'
import { Slider } from '../components/slider/slider'
import { SearchBox } from '../components/search-box/search-box'
import { DropDown } from '../components/drop-down/drop-down'
import { TabItem } from '../components/tab-item/tab-item'
import { TabGroup } from '../components/tab-group/tab-group'
import { Plus, Search, Settings, Heart, Download, Trash, Moon, Sun, Bell, Volume2, Sliders, Home, User, FileText, Image, File, Folder } from 'lucide-react'

interface ComponentsShowcaseProps {
  theme: 'light' | 'dark';
  onThemeChange: (theme: 'light' | 'dark') => void;
}

export const ComponentsShowcase: React.FC<ComponentsShowcaseProps> = ({ theme, onThemeChange }) => {
  const [count, setCount] = useState(0);
  
  return (
    <div className={`app-container theme-${theme}`}>
      <header>
        <h1>慧通组件库 - 组件展示</h1>
        <IconButton 
          icon={theme === 'dark' ? Sun : Moon} 
          onClick={() => onThemeChange(theme === 'dark' ? 'light' : 'dark')}
          size="large"
        />
      </header>

      <div className="demo-section">
        <h2>基本图标按钮</h2>
        <div className="buttons-row">
          <IconButton icon={Plus} onClick={() => alert('添加')} />
          <IconButton icon={Search} onClick={() => alert('搜索')} />
          <IconButton icon={Settings} onClick={() => alert('设置')} />
          <IconButton icon={Heart} onClick={() => alert('收藏')} />
        </div>
      </div>

      <div className="demo-section">
        <h2>不同尺寸</h2>
        <div className="buttons-row">
          <IconButton icon={Plus} size="small" />
          <IconButton icon={Plus} size="medium" />
          <IconButton icon={Plus} size="large" />
        </div>
      </div>

      <div className="demo-section">
        <h2>禁用状态</h2>
        <div className="buttons-row">
          <IconButton icon={Trash} disabled />
          <IconButton icon={Download} disabled />
        </div>
      </div>

      <div className="demo-section">
        <h2>按钮计数器</h2>
        <div className="buttons-row counter-demo">
          <IconButton icon={Plus} onClick={() => setCount(count + 1)} />
          <span className="counter">{count}</span>
          <IconButton 
            icon={Trash} 
            onClick={() => setCount(0)}
            disabled={count === 0}
          />
        </div>
      </div>

      <div className="demo-section">
        <h2>通知图标</h2>
        <div className="buttons-row">
          <div className="notification-button">
            <IconButton icon={Bell} onClick={() => alert('通知')} />
            <span className="notification-badge">5</span>
          </div>
        </div>
      </div>

      <div className="demo-section">
        <h2>主要按钮 (Primary Button)</h2>
        <div className="buttons-row">
          <PrimaryButton icon={Plus}>添加</PrimaryButton>
          <PrimaryButton icon={Download}>下载</PrimaryButton>
          <PrimaryButton disabled>禁用</PrimaryButton>
          <PrimaryButton fullWidth>满宽按钮</PrimaryButton>
          <PrimaryButton size="large">大尺寸</PrimaryButton>
        </div>
      </div>

      <div className="demo-section">
        <h2>次要按钮 (Secondary Button)</h2>
        <div className="buttons-row">
          <SecondaryButton icon={Plus}>添加</SecondaryButton>
          <SecondaryButton icon={Settings}>设置</SecondaryButton>
          <SecondaryButton disabled>禁用</SecondaryButton>
          <SecondaryButton fullWidth>满宽按钮</SecondaryButton>
          <SecondaryButton size="large">大尺寸</SecondaryButton>
        </div>
      </div>

      <div className="demo-section">
        <h2>滑块 (Slider)</h2>
        
        <Slider 
          onChange={(value) => console.log('滑块值:', value)}
          defaultValue={30}
        />

        <div className="volume-slider" style={{ marginTop: '24px' }}>
          <Volume2 size={20} />
          <Slider 
            defaultValue={60}
            onChange={(value) => console.log('音量:', value)}
            width={240}
          />
        </div>

        <div style={{ marginTop: '24px' }}>
          <Slider 
            min={0}
            max={200}
            step={10}
            defaultValue={100}
            onChange={(value) => console.log('步长:', value)}
          />
        </div>

        <div style={{ marginTop: '24px' }}>
          <Slider 
            disabled
            defaultValue={50}
          />
        </div>
      </div>

      <div className="demo-section">
        <h2>搜索框 (Search Box)</h2>
        
        <SearchBox 
          onChange={(value) => console.log('输入:', value)}
          onSearch={(value) => console.log('搜索:', value)}
          width={300}
        />

        <SearchBox 
          placeholder="自定义搜索提示"
          onChange={(value) => console.log('输入:', value)}
          style={{ marginTop: '16px' }}
          width={300}
        />

        <SearchBox 
          defaultValue="预填充内容"
          onChange={(value) => console.log('输入:', value)}
          style={{ marginTop: '16px' }}
          width={300}
        />

        <SearchBox 
          disabled
          placeholder="禁用状态"
          style={{ marginTop: '16px' }}
          width={300}
        />
      </div>

      <div className="demo-section">
        <h2>下拉框 (Dropdown)</h2>
        
        <div className="dropdown-demo">
          <h3>基本使用</h3>
          <DropDown 
            options={[
              { value: '1', label: '选项一' },
              { value: '2', label: '选项二' },
              { value: '3', label: '选项三' },
            ]}
            onChange={(value) => console.log('选择:', value)}
            placeholder="请选择"
            width={200}
          />
        </div>

        <div className="dropdown-demo">
          <h3>带禁用选项</h3>
          <DropDown 
            options={[
              { value: '1', label: '正常选项' },
              { value: '2', label: '禁用选项', disabled: true },
              { value: '3', label: '正常选项' },
            ]}
            placeholder="包含禁用选项"
          />
        </div>

        <div className="dropdown-demo">
          <h3>不同尺寸</h3>
          <div style={{ display: 'flex', gap: '16px', alignItems: 'flex-start' }}>
            <DropDown 
              size="small"
              options={[
                { value: '1', label: '选项一' },
                { value: '2', label: '选项二' },
              ]}
              placeholder="小尺寸"
              width={120}
            />
            <DropDown 
              options={[
                { value: '1', label: '选项一' },
                { value: '2', label: '选项二' },
              ]}
              placeholder="默认尺寸"
              width={120}
            />
            <DropDown 
              size="large"
              options={[
                { value: '1', label: '选项一' },
                { value: '2', label: '选项二' },
              ]}
              placeholder="大尺寸"
              width={120}
            />
          </div>
        </div>

        <div className="dropdown-demo">
          <h3>禁用状态</h3>
          <DropDown 
            disabled
            options={[
              { value: '1', label: '选项一' },
              { value: '2', label: '选项二' },
            ]}
            placeholder="禁用下拉框"
          />
        </div>
      </div>
      
      <div className="demo-section">
        <h2>标签项 (Tab Item)</h2>
        
        <div className="tab-demo">
          <h3>默认状态</h3>
          <div className="tab-row" style={{ display: 'flex', gap: '16px' }}>
            <TabItem
              label="模型"
              icon={FileText}
              onClick={() => console.log('点击模型标签')}
            />
            <TabItem
              label="设置"
              icon={Settings}
              onClick={() => console.log('点击设置标签')}
            />
            <TabItem
              label="首页"
              icon={Home}
              onClick={() => console.log('点击首页标签')}
            />
          </div>
        </div>
        
        <div className="tab-demo">
          <h3>选中状态</h3>
          <div className="tab-row" style={{ display: 'flex', gap: '16px', marginTop: '16px' }}>
            <TabItem
              label="模型"
              icon={FileText}
              isActive={true}
            />
            <TabItem
              label="设置"
              icon={Settings}
            />
            <TabItem
              label="首页"
              icon={Home}
            />
          </div>
        </div>
        
        <div className="tab-demo">
          <h3>自定义宽度</h3>
          <div className="tab-row" style={{ display: 'flex', gap: '16px', marginTop: '16px' }}>
            <TabItem
              label="短标签"
              icon={Sliders}
              width={80}
            />
            <TabItem
              label="较长的标签项"
              icon={User}
              width={150}
            />
          </div>
        </div>
        
        <div className="tab-demo">
          <h3>无图标标签</h3>
          <div className="tab-row" style={{ display: 'flex', gap: '16px', marginTop: '16px' }}>
            <TabItem
              label="选项一"
            />
            <TabItem
              label="选项二"
              isActive={true}
            />
            <TabItem
              label="选项三"
            />
          </div>
        </div>
      </div>
      
      <div className="demo-section">
        <h2>标签组 (Tab Group) - 点击切换状态</h2>
        
        <div className="tab-demo">
          <h3>基本使用</h3>
          <TabGroup>
            <TabItem
              label="模型"
              icon={FileText}
            />
            <TabItem
              label="设置"
              icon={Settings}
            />
            <TabItem
              label="首页"
              icon={Home}
            />
          </TabGroup>
        </div>
        
        <div className="tab-demo">
          <h3>自定义间距</h3>
          <TabGroup gap={24}>
            <TabItem
              label="图片"
              icon={Image}
            />
            <TabItem
              label="文件"
              icon={File}
            />
            <TabItem
              label="文件夹"
              icon={Folder}
            />
          </TabGroup>
        </div>
        
        <div className="tab-demo">
          <h3>默认选中索引</h3>
          <TabGroup defaultActiveIndex={1}>
            <TabItem
              label="选项一"
            />
            <TabItem
              label="选项二"
            />
            <TabItem
              label="选项三"
            />
          </TabGroup>
        </div>
        
        <div className="tab-demo">
          <h3>带状态变化回调</h3>
          <TabGroup onChange={(index) => console.log('当前选中索引:', index)}>
            <TabItem
              label="监听状态变化的标签一"
              width={160}
            />
            <TabItem
              label="监听状态变化的标签二"
              width={160}
            />
          </TabGroup>
        </div>
      </div>
    </div>
  );
}

// 导出已在组件声明处完成
