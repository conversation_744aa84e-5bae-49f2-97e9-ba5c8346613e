.render-page {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 20px;
  box-sizing: border-box;
  background: var(--color-bg-page);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  color: var(--color-content-accent);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 8px;
  width: 100%;
  margin-bottom: 20px;
}

.logo {
  width: 78px;
  height: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  object-fit: contain;
}

.user-controls {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
}

.render-container {
  display: flex;
  flex: 1;
  gap: 20px;
  overflow: hidden;
  padding: 0;
  box-sizing: border-box;
  max-width: 100%;
  width: 100%;
  margin: 0;
}

.render-window {
  flex: 1;
  min-width: 0;
  background: var(--color-bg-primary);
  border-radius: var(--radius-l);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.button-container {
  position: absolute;
  right: 20px;
  bottom: 20px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
}

.control-button {
  padding: 4px 8px;
  display: flex;
  align-items: center;
  gap: 6px;
  background: var(--color-bg-input);
  border-radius: var(--radius-full);
  cursor: pointer;
}

.control-button span {
  color: var(--color-content-regular);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.icon-wrapper {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-content-regular, rgba(255, 255, 255, 0.70));
}

.property-panel {
  /* 220px = 60*3 + 8*2 + 12*2，正好一行放3个材质球 */
  width: 220px;
  flex-shrink: 0;
  padding: 12px;
  background: var(--color-bg-primary);
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  /* enable vertical scrolling within the panel */
  overflow-y: auto;
  overflow-x: hidden;
}

.panel-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 4px;
}

.section-header span {
  color: var(--color-content-regular);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.dropdown-wrapper,
.upload-button-wrapper,
.search-wrapper {
  width: 100%;
}

.bottom-buttons {
  margin-top: auto;
  display: flex;
  justify-content: center;
  gap: 8px;
  padding-top: 12px;
}

.material-thumbnails {
  display: flex;
  justify-content: flex-start;
  gap: 8px;
}

/* .tab-switch {
  height: 36px;
  padding: 4px;
  background: var(--color-bg-overlay);
  border-radius: var(--radius-m);
  display: flex;
  align-items: center;
  gap: 4px;
}

.tab-item {
  flex: 1;
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 6px;
  color: var(--color-content-regular);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
}

.tab-item.active {
  background: var(--color-support);
  color: var(--color-content-accent);
} */

.materials-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-height: 0;
  overflow: visible;
}

.render-page .materials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
  gap: 8px;
  overflow-y: auto;
  padding: 4px;
  margin: -4px;
  align-content: flex-start;
  flex: 1;
  min-height: 0;
}

.render-page .material-item {
  aspect-ratio: 1/1;
  width: 100%;
  height: auto;
  background: var(--color-bg-input, rgba(255, 255, 255, 0.05));
  border-radius: var(--radius-m);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.render-page .material-item.active {
  border: 1px solid var(--color-brand, #2269EC);
}

/* 滚动条样式 - 隐藏滚动条 */

.content-area {
  display: none;
}

.render-area {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-bg-primary);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
  overflow: hidden;
  position: relative;
}

/* 3D渲染相关样式 */
.render-area canvas {
  width: 100% !important;
  height: 100% !important;
  outline: none;
}

/* 加载动画样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-content-mute);
  border-radius: 50%;
  border-top: 3px solid var(--color-brand, #2269EC);
  animation: spin 1s linear infinite;
}

.loading-text {
  color: var(--color-content-accent, rgba(255, 255, 255, 0.9));
  font-size: 14px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}


