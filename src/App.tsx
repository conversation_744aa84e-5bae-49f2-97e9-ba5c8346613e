import { useState } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'

// 页面导入
import RenderPage from './pages/RenderPage'
import Login from './pages/admin/Login'
import Dashboard from './pages/admin/Dashboard'
import { ComponentsShowcase } from './pages/ComponentsShowcase'

function App() {
  const [theme, setTheme] = useState<'light' | 'dark'>('dark')
  
  // 检查用户是否已登录（用于后台管理系统）
  const isLoggedIn = () => localStorage.getItem('isLoggedIn') === 'true'
  
  // 受保护的路由 - 只有登录后才能访问
  const ProtectedRoute = ({ children }: { children: React.ReactElement }) => {
    return isLoggedIn() ? children : <Navigate to="/admin" />
  }

  return (
    <Router>
      <div className={`app-container theme-${theme}`}>
        <Routes>
          <Route path="/" element={<RenderPage theme={theme} />} />
          <Route path="/admin" element={<Login />} />
          <Route path="/admin/dashboard" element={
            <ProtectedRoute>
              <Dashboard />
            </ProtectedRoute>
          } />
          <Route path="/components" element={
            <ComponentsShowcase 
              theme={theme} 
              onThemeChange={(newTheme: 'light' | 'dark') => setTheme(newTheme)} 
            />
          } />
        </Routes>
      </div>
    </Router>
  );
}

export default App
