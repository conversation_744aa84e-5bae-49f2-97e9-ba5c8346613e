import React, { memo, useRef } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { Environment } from '@react-three/drei';
import * as THREE from 'three';
import type { MaterialData } from '../../services/api';

interface MaterialThumbnailSimpleProps {
  material: MaterialData;
  size?: number;
}

// 旋转动画组件
const RotatingSphere: React.FC<{ material: MaterialData }> = ({ material }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  
  useFrame(() => {
    if (meshRef.current) {
      meshRef.current.rotation.y += 0.01;
    }
  });

  return (
    <mesh ref={meshRef}>
      <sphereGeometry args={[0.8, 32, 32]} />
      <meshStandardMaterial
        color={new THREE.Color(material.color)}
        metalness={material.metalness / 100}
        roughness={material.roughness / 100}
        transparent={material.glass > 0}
        opacity={material.glass > 0 ? (100 - material.glass) / 100 : 1}
      />
    </mesh>
  );
};

/**
 * 简化的材质缩略图组件，专为表格中的缩略图设计
 */
const MaterialThumbnailSimple: React.FC<MaterialThumbnailSimpleProps> = ({ 
  material, 
  size = 40 
}) => {
  const canvasRef = useRef<HTMLDivElement>(null);
  
  return (
    <div 
      ref={canvasRef}
      style={{
        width: size,
        height: size,
        borderRadius: '50%',
        overflow: 'hidden',
        background: 'var(--color-input-background, rgba(255, 255, 255, 0.05))',
        position: 'relative'
      }}
    >
      <Canvas
        frameloop="always"
        camera={{ position: [0, 0, 1.5], fov: 45 }}
        gl={{ antialias: true, alpha: true }}
        style={{
          width: '100%',
          height: '100%',
          display: 'block'
        }}
      >
        <ambientLight intensity={0.7} />
        <directionalLight position={[5, 5, 5]} intensity={1} />
        <RotatingSphere material={material} />
        <Environment preset="city" />
      </Canvas>
    </div>
  );
};

export default memo(MaterialThumbnailSimple);
