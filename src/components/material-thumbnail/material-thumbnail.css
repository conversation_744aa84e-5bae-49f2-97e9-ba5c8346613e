.material-item {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  position: relative;
  background: var(--color-input-background, rgba(255, 255, 255, 0.05));
  display: flex;
  align-items: center;
  justify-content: center;
}

.thumbnail-canvas {
  width: 60%;
  height: 60%;
  border-radius: 50%;
  overflow: hidden;
}

.material-item canvas {
  width: 100% !important;
  height: 100% !important;
  display: block;
}

.material-item.active {
  outline: 1px solid var(--color-brand, #2269EC);
  outline-offset: 0;
  border-radius: 8px;
}
